const axios = require('axios');

// Configuration
const SERVER_URL = 'https://nox.secretservercloud.com/';
const API_KEY = 'AgJoesAm_9S4AmI1kYmiRFvgqGrcoQkzsYqG1UqS18Jsi6rRygv7JMJW3rc6iGD2CeOmcRfEOJR_a9CxTEVRfqUveekDQzeUOxQbCzps-Mp1YG6FL2V99ryfENKAjJod_bHLvTlLSZ7PD4WlBXqLXMv73EjK6UYzXSYwRCfsqz0K66WXFDqqBUbNTdJQrokJr64hkYHb4dQA75RPoTgCQk3jKmV6hXbX7dt3os_9Z7IqEV4rUj3R-oAVvZOYEan4WjBuhKqKZ_eR3zt-ir2Cpl8ka5X2YHdtg4zD1lk7116Yml9I3VmITgTJ48laabbOHccBMuovTIk8atg0yU_2JCuMQOQX1R6U_23fgEHYrJVo6fmaPdbjvZhzFFktFpr1fmusiGwUFezMy890IlzTB_mhQMxkd0cwj_xtVP1y6Nn2V61josqY6kIC7L7yXLzeI5YwOy0jfoyiLNB2VKNeSt5wcHbm0I4SF-jVpdXB2hA9DAHzo0kUpKVp-GiAjYaFeb_OBLewEOjBG17J4eAImYUN8RWfGZQb3hVvDrR4xoL2iBvXd9501WQNdB5Dr-iRhkxxeeiSmNptP4a1LNmXd75lvXOa_S8h3fQ9bf1CrJQhFDlhgWRGG00vSst8QnmKg0Y334YTfBvqDinGws1-4Ty6dYIb0MpEPSFV2dxem3XBbTFT5qVR2_dlCU7yuR__IcmW0fHGsxmHhG3MVSLdJFBC';

// Configuration for Password template (Template ID 2)
const SECRET_TEMPLATE_ID = 2; // Password template ID
const SITE_ID = 1;
const FOLDER_ID = 6841; // User's personal folder ID (from previous discovery)

// Function to get common headers
function getHeaders() {
  return {
    'Authorization': `Bearer ${API_KEY}`,
    'Content-Type': 'application/json'
  };
}

// Function to create expiration date (120 days from now)
function getExpirationDate() {
  const now = new Date();
  const expirationDate = new Date(now.getTime() + (120 * 24 * 60 * 60 * 1000)); // 120 days
  return expirationDate.toISOString();
}

// Function to create a password secret using Template 2
async function createPasswordSecret(secretName, username, password, notes, folderId = FOLDER_ID) {
  try {
    const expirationDate = getExpirationDate();
    
    const payload = {
      SecretTemplateId: SECRET_TEMPLATE_ID,
      Name: secretName,
      SiteId: SITE_ID,
      FolderId: folderId,
      Items: [
        { FieldName: 'Username', ItemValue: username },
        { FieldName: 'Password', ItemValue: password },
        { FieldName: 'Notes', ItemValue: notes }
      ],
      ExpirationDate: expirationDate,
      EnableHeartbeat: true,
      HeartbeatEnabled: true
    };

    console.log(`Creating secret "${secretName}" with Password template...`);
    console.log('Payload:', JSON.stringify(payload, null, 2));

    const response = await axios.post(`${SERVER_URL}/api/v1/secrets`, payload, {
      headers: getHeaders()
    });

    console.log(`✓ Secret "${secretName}" created successfully!`);
    console.log(`Secret ID: ${response.data.id}`);
    console.log(`Expiration Date: ${expirationDate}`);
    console.log(`Heartbeat Enabled: ${payload.HeartbeatEnabled}`);
    
    return response.data;

  } catch (error) {
    if (error.response) {
      console.error('Error status:', error.response.status);
      console.error('Error data:', error.response.data);
    } else if (error.request) {
      console.error('No response received:', error.request);
    } else {
      console.error('Request setup error:', error.message);
    }
    return null;
  }
}

// Function to share a secret with a user
async function shareSecret(secretId, userId, permissions = ['View']) {
  try {
    const accessRole = permissions[0] || 'View';
    
    const payload = {
      secretAccessRoleName: accessRole,
      secretId: secretId,
      userId: userId
    };

    console.log(`Sharing secret ${secretId} with user ID ${userId} (permissions: ${permissions.join(', ')})...`);

    const response = await axios.post(`${SERVER_URL}/api/v1/secret-permissions`, payload, {
      headers: getHeaders()
    });

    console.log(`✓ Secret shared successfully with user ID ${userId}!`);
    return response.data;

  } catch (error) {
    if (error.response) {
      console.error('Error status:', error.response.status);
      console.error('Error data:', error.response.data);
    } else {
      console.error('Request error:', error.message);
    }
    return null;
  }
}

// Function to verify template 2 exists and get its structure
async function verifyPasswordTemplate() {
  try {
    const response = await axios.get(`${SERVER_URL}/api/v1/secret-templates/${SECRET_TEMPLATE_ID}`, {
      headers: getHeaders()
    });
    
    console.log(`Template ID ${SECRET_TEMPLATE_ID} Details:`);
    console.log('Name:', response.data.name);
    console.log('Fields:');
    response.data.fields.forEach(field => {
      console.log(`  - ${field.displayName} (${field.name}) - Required: ${field.isRequired}`);
    });
    return response.data;
  } catch (error) {
    console.error('Template verification error:', error.response?.data || error.message);
    return null;
  }
}

// Main execution function
async function main() {
  console.log('=== Verifying Password Template (ID: 2) ===');
  const template = await verifyPasswordTemplate();
  
  if (!template) {
    console.log('❌ Template 2 not accessible. Exiting...');
    return;
  }

  console.log('\n=== Creating API Key Secret with Password Template ===');
  
  // Create the secret with your specified values
  const secretResult = await createPasswordSecret(
    'U-manage Api key',
    '<EMAIL>',
    '8LgZdBNI275JpPU/tw8s2w==:mSyuGM3sy3uJhKR9GaY3Y69NrDmG7pR6tL53RG8c3RcnAIfNXZ4ypztMLIjuCaAIa6hoVv6XJ86HcE0vSAUTyg==',
    'Auto Generated Key from U-manage'
  );

  if (secretResult && secretResult.id) {
    console.log('\n📋 Summary:');
    console.log(`   Secret Name: U-manage Api key`);
    console.log(`   Secret ID: ${secretResult.id}`);
    console.log(`   Username: <EMAIL>`);
    console.log(`   Template: Password (ID: 2)`);
    console.log(`   Folder ID: ${FOLDER_ID}`);
    console.log(`   Expiration: 120 days from creation`);
    console.log(`   Heartbeat: Enabled`);
    console.log(`   Notes: Auto Generated Key from U-manage`);
    
    // Uncomment the line below if you want to share with a specific user ID
    // await shareSecret(secretResult.id, USER_ID_TO_SHARE_WITH, ['View']);
    
  } else {
    console.log('❌ Failed to create secret');
  }
}

// Run the main function
main().catch(console.error);
