const axios = require('axios');
 
// Configuration
const SERVER_URL = 'https://nox.secretservercloud.com/';
const API_KEY = 'AgJoesAm_9S4AmI1kYmiRFvgqGrcoQkzsYqG1UqS18Jsi6rRygv7JMJW3rc6iGD2CeOmcRfEOJR_a9CxTEVRfqUveekDQzeUOxQbCzps-Mp1YG6FL2V99ryfENKAjJod_bHLvTlLSZ7PD4WlBXqLXMv73EjK6UYzXSYwRCfsqz0K66WXFDqqBUbNTdJQrokJr64hkYHb4dQA75RPoTgCQk3jKmV6hXbX7dt3os_9Z7IqEV4rUj3R-oAVvZOYEan4WjBuhKqKZ_eR3zt-ir2Cpl8ka5X2YHdtg4zD1lk7116Yml9I3VmITgTJ48laabbOHccBMuovTIk8atg0yU_2JCuMQOQX1R6U_23fgEHYrJVo6fmaPdbjvZhzFFktFpr1fmusiGwUFezMy890IlzTB_mhQMxkd0cwj_xtVP1y6Nn2V61josqY6kIC7L7yXLzeI5YwOy0jfoyiLNB2VKNeSt5wcHbm0I4SF-jVpdXB2hA9DAHzo0kUpKVp-GiAjYaFeb_OBLewEOjBG17J4eAImYUN8RWfGZQb3hVvDrR4xoL2iBvXd9501WQNdB5Dr-iRhkxxeeiSmNptP4a1LNmXd75lvXOa_S8h3fQ9bf1CrJQhFDlhgWRGG00vSst8QnmKg0Y334YTfBvqDinGws1-4Ty6dYIb0MpEPSFV2dxem3XBbTFT5qVR2_dlCU7yuR__IcmW0fHGsxmHhG3MVSLdJFBC'
// You must set these values according to your company's Secret Server configuration
const SECRET_TEMPLATE_ID = 6062; // Example: 1 for "Generic Password", ask your admin for correct value
const SITE_ID = 1;

// Function to create a secret
async function createSecret(name, secretValue) {
  try {
    const payload = {
      SecretTemplateId: SECRET_TEMPLATE_ID,
      Name: name,
      SiteId: SITE_ID,
      Items: [
        { FieldName: 'Password', ItemValue: secretValue }
      ]
    };

    const response = await axios.post(`${SERVER_URL}/api/v1/secrets`, payload, {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('Secret Created:', response.data);
  } catch (error) {
    if (error.response) {
      console.error('Error status:', error.response.status);
      console.error('Error data:', error.response.data);
    } else if (error.request) {
      console.error('No response received:', error.request);
    } else {
      console.error('Request setup error:', error.message);
    }
  }
}

// Add this function to check template fields
async function getSecretTemplate() {
  try {
    const response = await axios.get(`${SERVER_URL}/api/v1/secret-templates/${SECRET_TEMPLATE_ID}`, {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('Template Fields:', response.data.fields);
    return response.data;
  } catch (error) {
    console.error('Template fetch error:', error.response?.data || error.message);
  }
}

// Call this first to see required fields
getSecretTemplate();

createSecret('test1', 'qwertyuiopas');
// Example
